// components/Dashboard/ProjectInsights.tsx
import { Card } from "@/components/ui/card";
import { Calendar, ChevronDown, ArrowUp } from "lucide-react";
import { ChartData } from "@/types/dashboard";

interface ProjectInsightsProps {
  chartData: ChartData[];
}

export const ProjectInsights: React.FC<ProjectInsightsProps> = ({
  chartData,
}) => {
  return (
    <div className="xl:col-span-2">
      <Card className="p-6 bg-white/80 backdrop-blur-sm border-slate-200/50 shadow-xl shadow-slate-200/20">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-slate-900 mb-2">
              Project Insights
            </h2>
            <p className="text-sm text-slate-600 max-w-md">
              Weekly task completion and hours logged overview
            </p>
          </div>
          <div className="flex items-center gap-2 px-3 py-2 bg-slate-50 rounded-xl border border-slate-200/50">
            <Calendar className="w-4 h-4 text-slate-500" />
            <span className="text-sm text-slate-700">This Week</span>
            <ChevronDown className="w-4 h-4 text-slate-500" />
          </div>
        </div>

        {/* Chart */}
        <div className="relative h-80">
          <div className="absolute inset-0 bg-gradient-to-t from-slate-50/50 to-transparent rounded-xl"></div>
          <div className="relative flex items-end justify-center h-full gap-6 px-4">
            {chartData.map((item, index) => (
              <div
                key={index}
                className="flex flex-col items-center gap-3 group"
              >
                <div
                  className={`w-8 ${
                    item.height
                  } relative rounded-t-lg transition-all duration-300 group-hover:scale-110 ${
                    item.active
                      ? "bg-gradient-to-t from-indigo-600 to-indigo-400 shadow-lg shadow-indigo-200"
                      : "bg-gradient-to-t from-slate-200 to-slate-100 hover:from-indigo-200 hover:to-indigo-100"
                  }`}
                >
                  {item.active && (
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-slate-900 text-white text-xs px-2 py-1 rounded-md">
                      {item.value} projects
                    </div>
                  )}
                  <div
                    className={`absolute top-2 left-1/2 transform -translate-x-1/2 w-2 h-2 rounded-full ${
                      item.active ? "bg-white/80" : "bg-indigo-400"
                    }`}
                  ></div>
                </div>
                <span
                  className={`text-sm font-medium ${
                    item.active ? "text-slate-900" : "text-slate-500"
                  }`}
                >
                  {item.day}
                </span>
              </div>
            ))}
          </div>

          {/* Grid Lines */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className="absolute w-full border-t border-slate-200/30"
                style={{ top: `${25 * (i + 1)}%` }}
              ></div>
            ))}
          </div>
        </div>

        {/* Chart Footer */}
        <div className="mt-6 p-4 bg-gradient-to-r from-slate-50 to-slate-100/50 rounded-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-indigo-500 rounded-full"></div>
                <span className="text-sm text-slate-600">This week</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-slate-300 rounded-full"></div>
                <span className="text-sm text-slate-600">Previous weeks</span>
              </div>
            </div>
            <div className="flex items-center gap-1 text-green-600">
              <ArrowUp className="w-4 h-4" />
              <span className="text-sm font-medium">+23% from last week</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
