// components/Dashboard/RecentProjects.tsx
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Project } from "@/types/dashboard";

interface RecentProjectsProps {
  projects: Project[];
}

export const RecentProjects: React.FC<RecentProjectsProps> = ({ projects }) => {
  return (
    <Card className="p-6 bg-white/80 backdrop-blur-sm border-slate-200/50">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-slate-900">
          Recent Projects
        </h3>
        <button className="text-sm text-indigo-600 hover:text-indigo-700 font-medium">
          View all
        </button>
      </div>
      <div className="space-y-4">
        {projects.map((project) => (
          <div
            key={project.id}
            className="p-3 rounded-xl bg-slate-50/80 border border-slate-100"
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                <div className={`w-2 h-2 ${project.color} rounded-full`}></div>
                <span className="text-sm font-medium text-slate-900">
                  {project.name}
                </span>
              </div>
              <Badge
                className={`text-xs px-2 py-1 ${
                  project.status === "Paid"
                    ? "bg-green-100 text-green-700 border-green-200"
                    : "bg-orange-100 text-orange-700 border-orange-200"
                }`}
              >
                {project.status}
              </Badge>
            </div>
            <div className="flex items-center gap-2 text-xs text-slate-600">
              <div className="flex-1 bg-slate-200 rounded-full h-1.5">
                <div
                  className="bg-indigo-500 h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
              <span className="font-medium">{project.progress}%</span>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};
